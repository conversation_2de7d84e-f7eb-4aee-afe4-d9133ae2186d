using Application.Abstractions.Storage;
using Infrastructure.Common;
using Microsoft.AspNetCore.Http;
using Minio;
using Minio.DataModel;
using Minio.DataModel.Args;
using Polly;
using Polly.Registry;
using Shared;

namespace Infrastructure.Storage;

public class MinioStorageService : IStorageService
{
    private readonly string _baseUrl;
    private readonly string _bucketName;
    private readonly IMinioClient _client;
    private readonly ResiliencePipeline _pipeline;

    public MinioStorageService(
        IMinioClient client,
        string baseUrl,
        string bucketName,
        ResiliencePipelineProvider<string> pipelineProvider
    )
    {
        _client = client;
        _baseUrl = baseUrl;
        _bucketName = bucketName;
        _pipeline = pipelineProvider.GetPipeline(ResilienceKey.MINIO);
    }

    public async Task<Result<string>> GetPresignedUrl(
        string folder,
        string fileName,
        TimeSpan? expiresIn,
        CancellationToken cancellationToken = default
    )
    {
        TimeSpan expiry = expiresIn ?? TimeSpan.FromHours(1);
        string objectName = Path.Combine(folder, fileName);
        PresignedGetObjectArgs? requestObject = new PresignedGetObjectArgs()
            .WithBucket(_bucketName)
            .WithObject(objectName)
            .WithExpiry(expiry.Seconds);
        string? url = await _pipeline.ExecuteAsync(
            async _ => await _client.PresignedGetObjectAsync(requestObject),
            cancellationToken
        );
        if (string.IsNullOrWhiteSpace(url))
        {
            return Result<string>.Failure(
                Error.Failure("MinioStorage.GetFileUrl", "File not found.")
            );
        }

        return Result<string>.Success(url);
    }

    public async Task<Result<FileMetadata>> GetFileMetadataAsync(
        string folder,
        string fileName,
        CancellationToken cancellationToken = default
    )
    {
        string objectName = Path.Combine(folder, fileName);
        StatObjectArgs statObjectArgs = new StatObjectArgs()
            .WithBucket(_bucketName)
            .WithObject(objectName);
        ObjectStat? stat = await _pipeline.ExecuteAsync(
            async (token) => await _client.StatObjectAsync(statObjectArgs, token),
            cancellationToken
        );
        if (stat is null)
        {
            return Result<FileMetadata>.Failure(
                Error.Failure("MinioStorage.GetFileMetadata", "File not found.")
            );
        }

        string name = stat.ObjectName.Split("/").Last();
        string publicUrl = Path.Combine(_baseUrl, _bucketName, folder, fileName);
        return new FileMetadata(
            name,
            stat.ContentType,
            stat.Size,
            stat.ETag,
            publicUrl,
            stat.LastModified
        );
    }

    public async Task<Result<FileMetadata>> UploadAsync(
        string folder,
        IFormFile file,
        CancellationToken cancellationToken = default
    )
    {
        string fileName = $"{Guid.NewGuid()}{Path.GetExtension(file.FileName)}";
        string filePath = Path.Combine(folder, fileName);
        PutObjectArgs args = new PutObjectArgs()
            .WithBucket(_bucketName)
            .WithObject(filePath)
            .WithStreamData(file.OpenReadStream())
            .WithObjectSize(file.Length)
            .WithContentType(file.ContentType);

        await _pipeline.ExecuteAsync(
            async (token) => await _client.PutObjectAsync(args, token),
            cancellationToken
        );
        Result<FileMetadata> metaData = await GetFileMetadataAsync(
            folder,
            fileName,
            cancellationToken
        );

        return Result<FileMetadata>.Success(metaData.Value);
    }

    public async Task<Result<FileMetadata>> UploadAsync(
        string folder,
        string fileName,
        Stream stream,
        CancellationToken cancellationToken = default
    )
    {
        string filePath = Path.Combine(folder, fileName);
        PutObjectArgs args = new PutObjectArgs()
            .WithBucket(_bucketName)
            .WithObject(filePath)
            .WithStreamData(stream)
            .WithObjectSize(stream.Length);

        await _pipeline.ExecuteAsync(
            async (token) => await _client.PutObjectAsync(args, token),
            cancellationToken
        );
        Result<FileMetadata> metaData = await GetFileMetadataAsync(
            folder,
            fileName,
            cancellationToken
        );

        return Result<FileMetadata>.Success(metaData.Value);
    }

    public async Task<Result<List<FileMetadata>>> UploadBulkAsync(
        string folder,
        IFormFileCollection files,
        CancellationToken cancellationToken = default
    )
    {
        List<FileMetadata> metadatas = [];
        foreach (IFormFile file in files)
        {
            Result<FileMetadata> result = await UploadAsync(folder, file, cancellationToken);
            if (result.IsFailure)
            {
                return Result<List<FileMetadata>>.Failure(result.Error);
            }
            metadatas.Add(result.Value);
        }

        return Result<List<FileMetadata>>.Success(metadatas);
    }

    public async Task<Result<List<FileMetadata>>> UploadBulkAsync(
        string folder,
        ICollection<IFormFile> files,
        CancellationToken cancellationToken = default
    )
    {
        List<FileMetadata> metadatas = [];
        foreach (IFormFile file in files)
        {
            Result<FileMetadata> result = await UploadAsync(folder, file, cancellationToken);
            if (result.IsFailure)
            {
                return Result<List<FileMetadata>>.Failure(result.Error);
            }
            metadatas.Add(result.Value);
        }

        return Result<List<FileMetadata>>.Success(metadatas);
    }

    public async Task<Result<List<FileMetadata>>> UploadBulkAsync(
        string folder,
        ICollection<(string fileName, Stream stream)> files,
        CancellationToken cancellationToken = default
    )
    {
        List<FileMetadata> metadatas = [];
        foreach ((string fileName, Stream stream) in files)
        {
            Result<FileMetadata> result = await UploadAsync(
                folder,
                fileName,
                stream,
                cancellationToken
            );
            if (result.IsFailure)
            {
                return Result<List<FileMetadata>>.Failure(result.Error);
            }
            metadatas.Add(result.Value);
        }

        return Result<List<FileMetadata>>.Success(metadatas);
    }

    public async Task<Result> DeleteFileAsync(
        string folder,
        string fileName,
        CancellationToken cancellationToken = default
    )
    {
        string objectName = Path.Combine(folder, fileName);
        RemoveObjectArgs args = new RemoveObjectArgs()
            .WithBucket(_bucketName)
            .WithObject(objectName);
        await _pipeline.ExecuteAsync(
            async (token) => await _client.RemoveObjectAsync(args, token),
            cancellationToken
        );

        return Result.Success();
    }

    public async Task<Result> DeleteFolderAsync(
        string folder,
        CancellationToken cancellationToken = default
    )
    {
        IAsyncEnumerable<Item> objects = _client.ListObjectsEnumAsync(
            new ListObjectsArgs().WithBucket(_bucketName).WithPrefix(folder),
            cancellationToken
        );
        await foreach (Item item in objects)
        {
            await DeleteFileAsync(folder, item.Key, cancellationToken);
        }

        return Result.Success();
    }
}
