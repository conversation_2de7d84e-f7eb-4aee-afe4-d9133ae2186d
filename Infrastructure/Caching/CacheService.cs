using System.Text;
using Application.Abstractions.Caching;
using Infrastructure.Common;
using Microsoft.Extensions.Caching.Distributed;
using Newtonsoft.Json;
using Polly;
using Polly.Registry;

namespace Infrastructure.Caching;

public class CacheService : ICacheService
{
    private readonly IDistributedCache _cache;
    private readonly ResiliencePipeline _pipeline;

    public CacheService(
        IDistributedCache cache,
        ResiliencePipelineProvider<string> pipelineProvider
    )
    {
        _cache = cache;
        _pipeline = pipelineProvider.GetPipeline(ResilienceKey.DISTRIBUTED_CACHE);
    }

    public async Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default)
    {
        // byte[]? value = await _cache.GetAsync(key, cancellationToken);
        byte[]? value = await _pipeline.ExecuteAsync(
            async (token) => await _cache.GetAsync(key, token),
            cancellationToken
        );
        return value is null
            ? default
            : JsonConvert.DeserializeObject<T>(Encoding.UTF8.GetString(value));
    }

    public async Task<T?> GetOrSetAsync<T>(
        string key,
        Func<Task<T>> factory,
        TimeSpan? expirationTime = null,
        CancellationToken cancellationToken = default
    )
    {
        // byte[]? value = await _cache.GetAsync(key, cancellationToken);
        byte[]? value = await _pipeline.ExecuteAsync(
            async (token) => await _cache.GetAsync(key, token),
            cancellationToken
        );
        if (value is not null)
        {
            return JsonConvert.DeserializeObject<T>(Encoding.UTF8.GetString(value));
        }

        T result = await factory();
        await _pipeline.ExecuteAsync(
            async (token) =>
                await _cache.SetAsync(
                    key,
                    Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(value)),
                    new DistributedCacheEntryOptions
                    {
                        AbsoluteExpirationRelativeToNow = expirationTime,
                    },
                    token
                ),
            cancellationToken
        );
        return result;
    }

    public async Task SetAsync<T>(
        string key,
        T value,
        TimeSpan? expirationTime = null,
        CancellationToken cancellationToken = default
    ) =>
        await _pipeline.ExecuteAsync(
            async (token) =>
                await _cache.SetAsync(
                    key,
                    Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(value)),
                    new DistributedCacheEntryOptions
                    {
                        AbsoluteExpirationRelativeToNow = expirationTime,
                    },
                    token
                ),
            cancellationToken
        );

    public async Task RemoveAsync(string key, CancellationToken cancellationToken = default) =>
        await _pipeline.ExecuteAsync(
            async (token) => await _cache.RemoveAsync(key, token),
            cancellationToken
        );
}
