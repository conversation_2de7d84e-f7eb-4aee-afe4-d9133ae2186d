using Application.Abstractions.Caching;
using Application.Abstractions.Encryption;
using Application.Abstractions.Persistent;
using Application.Abstractions.Storage;
using Application.Abstractions.Token;
using Infrastructure.Caching;
using Infrastructure.Common;
using Infrastructure.Persistent.Data;
using Infrastructure.Persistent.Interceptors;
using Infrastructure.Storage;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.StackExchangeRedis;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Minio;
using Minio.Exceptions;
using Polly;
using Polly.CircuitBreaker;
using Polly.Registry;
using Polly.Retry;
using Serilog;
using StackExchange.Redis;

namespace Infrastructure;

public static class DependencyInjection
{
    public static IServiceCollection AddInfrastructure(
        this IServiceCollection services,
        IConfigurationManager configuration
    ) =>
        services
            .AddDatabase(configuration)
            .AddRealtime()
            .AddResilientPolicies()
            .AddStorage(configuration)
            .AddCaching(configuration);

    private static IServiceCollection AddDatabase(
        this IServiceCollection services,
        IConfigurationManager configuration
    )
    {
        string? connectionString =
            // "Host=localhost;Port=5432;Username=postgres;Password=**************;Database=pocket_chat";
            Environment.GetEnvironmentVariable("ConnectionString__Postgres")
            ?? configuration.GetConnectionString("Default");

        if (string.IsNullOrWhiteSpace(connectionString))
        {
            // ignore for now
            // throw new Exception("Connection string not found.");
        }

        services.AddSingleton<AuditableInterceptor>();
        services.AddSingleton<SoftDeleteInterceptor>();

        services.AddDbContext<ApplicationDbContext>(
            (sp, options) =>
                options
                    .UseNpgsql(connectionString)
                    .EnableSensitiveDataLogging()
                    .LogTo(Log.Logger.Debug)
                    .UseSnakeCaseNamingConvention()
                    .AddInterceptors(sp.GetRequiredService<AuditableInterceptor>())
                    .AddInterceptors(sp.GetRequiredService<SoftDeleteInterceptor>())
        );

        services.AddScoped<IApplicationDbContext>(sp =>
            sp.GetRequiredService<ApplicationDbContext>()
        );
        services.AddScoped<IUnitOfWork, UnitOfWork>();
        return services;
    }

    private static IServiceCollection AddRealtime(this IServiceCollection services)
    {
        services.AddSignalR();
        return services;
    }

    private static IServiceCollection AddStorage(
        this IServiceCollection services,
        IConfigurationManager configuration
    )
    {
        string? connectionString =
            Environment.GetEnvironmentVariable("ConnectionString__Minio")
            ?? configuration.GetConnectionString("Minio");
        // "localhost:9000";
        if (string.IsNullOrWhiteSpace(connectionString))
        {
            // ignore for now
            // throw new Exception("Connection string not found.");
            return services;
        }

        string? minioUser = Environment.GetEnvironmentVariable("MINIO_ACCESS_KEY_ID") ?? "aiotech";
        string? minioPassword =
            Environment.GetEnvironmentVariable("MINIO_SECRET_ACCESS_KEY") ?? "4S1P7DC1bOH18IK";
        string? minioBucket =
            Environment.GetEnvironmentVariable("MINIO_BUCKET_NAME") ?? "pocketchat";

        if (
            string.IsNullOrWhiteSpace(minioUser)
            || string.IsNullOrWhiteSpace(minioPassword)
            || string.IsNullOrWhiteSpace(minioBucket)
        )
        {
            // Temporary ignore for migration purposes
            return services;
            // throw new Exception("Minio configuration not found.");
        }

        IMinioClient minioClient = new MinioClient()
            .WithEndpoint(connectionString)
            .WithCredentials(minioUser, minioPassword)
            .Build();

        ServiceProvider sp = services.BuildServiceProvider();
        ResiliencePipelineProvider<string> pipelineProvider = sp.GetRequiredService<
            ResiliencePipelineProvider<string>
        >();
        services.AddSingleton(minioClient);
        services.AddScoped<IStorageService>(provider => new MinioStorageService(
            provider.GetRequiredService<IMinioClient>(),
            connectionString,
            minioBucket,
            pipelineProvider
        ));
        return services;
    }

    private static IServiceCollection AddCaching(
        this IServiceCollection services,
        IConfigurationManager configuration
    )
    {
        string? connectionString =
            Environment.GetEnvironmentVariable("ConnectionString__Redis")
            ?? configuration.GetConnectionString("Connection_Redis");
        if (string.IsNullOrWhiteSpace(connectionString))
        {
            // ignore for now
            // throw new Exception("Connection string not found.");
        }

        services.AddStackExchangeRedisCache(options =>
        {
            options.Configuration = connectionString!;
            options.ConfigurationOptions = new ConfigurationOptions
            {
                AbortOnConnectFail = true,
                EndPoints = { options.Configuration },
            };
        });
        services.AddSingleton<ICacheService, CacheService>();
        return services;
    }

    private static IServiceCollection AddResilientPolicies(this IServiceCollection services)
    {
        services.AddResiliencePipeline(
            ResilienceKey.DISTRIBUTED_CACHE,
            (builder, context) =>
            {
                ILogger<RedisCache> logger = context.ServiceProvider.GetRequiredService<
                    ILogger<RedisCache>
                >();
                builder.AddRetry(
                    new RetryStrategyOptions()
                    {
                        MaxRetryAttempts = 3,
                        BackoffType = DelayBackoffType.Exponential,
                        Delay = TimeSpan.FromSeconds(1),
                        MaxDelay = TimeSpan.FromSeconds(10),
                        ShouldHandle = new PredicateBuilder()
                            .Handle<RedisException>()
                            .Handle<RedisTimeoutException>()
                            .Handle<RedisConnectionException>()
                            .Handle<TaskCanceledException>(),
                        OnRetry = args =>
                        {
                            logger.LogWarning(
                                "Redis Retry {AttemptNumber} after {Delay}ms. Exception: {Exception}",
                                args.AttemptNumber,
                                args.RetryDelay.TotalMilliseconds,
                                args.Outcome.Exception?.Message
                            );
                            return ValueTask.CompletedTask;
                        },
                    }
                );
                builder.AddCircuitBreaker(
                    new CircuitBreakerStrategyOptions()
                    {
                        FailureRatio = 0.5,
                        SamplingDuration = TimeSpan.FromSeconds(30),
                        MinimumThroughput = 3,
                        BreakDuration = TimeSpan.FromSeconds(30),
                        ShouldHandle = new PredicateBuilder()
                            .Handle<RedisException>()
                            .Handle<RedisTimeoutException>()
                            .Handle<RedisConnectionException>(),
                        OnOpened = args =>
                        {
                            logger.LogWarning("Redis Circuit breaker opened");
                            return ValueTask.CompletedTask;
                        },
                        OnClosed = args =>
                        {
                            logger.LogInformation("Redis Circuit breaker closed");
                            return ValueTask.CompletedTask;
                        },
                    }
                );
                builder.AddTimeout(TimeSpan.FromSeconds(10));
            }
        );

        services.AddResiliencePipeline(
            ResilienceKey.MINIO,
            (builder, context) =>
            {
                ILogger<MinioClient> logger = context.ServiceProvider.GetRequiredService<
                    ILogger<MinioClient>
                >();
                builder.AddRetry(
                    new RetryStrategyOptions()
                    {
                        MaxRetryAttempts = 3,
                        BackoffType = DelayBackoffType.Exponential,
                        Delay = TimeSpan.FromSeconds(1),
                        MaxDelay = TimeSpan.FromSeconds(10),
                        ShouldHandle = new PredicateBuilder()
                            .Handle<MinioException>()
                            .Handle<TaskCanceledException>(),
                        OnRetry = args =>
                        {
                            logger.LogWarning(
                                "Minio Retry {AttemptNumber} after {Delay}ms. Exception: {Exception}",
                                args.AttemptNumber,
                                args.RetryDelay.TotalMilliseconds,
                                args.Outcome.Exception?.Message
                            );
                            return ValueTask.CompletedTask;
                        },
                    }
                );
                builder.AddCircuitBreaker(
                    new CircuitBreakerStrategyOptions()
                    {
                        FailureRatio = 0.5,
                        SamplingDuration = TimeSpan.FromSeconds(30),
                        MinimumThroughput = 3,
                        BreakDuration = TimeSpan.FromSeconds(30),
                        ShouldHandle = new PredicateBuilder().Handle<MinioException>(),
                        OnOpened = args =>
                        {
                            logger.LogWarning("Minio Circuit breaker opened");
                            return ValueTask.CompletedTask;
                        },
                        OnClosed = args =>
                        {
                            logger.LogInformation("Minio Circuit breaker closed");
                            return ValueTask.CompletedTask;
                        },
                    }
                );
                builder.AddTimeout(TimeSpan.FromSeconds(30));
            }
        );
        return services;
    }
}
