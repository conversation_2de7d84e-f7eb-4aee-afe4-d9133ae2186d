using Application.Abstractions.Persistent;
using Domain.AuditLogs;
using Domain.Categories;
using Domain.Courses;
using Domain.Lessons;
using Domain.Payments;
using Domain.Progresses;
using Domain.Subscriptions;
using Domain.Users;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Persistent.Data;

public sealed class ApplicationDbContext : DbContext, IApplicationDbContext
{
    public DbSet<AuditLog> AuditLogs { get; set; }
    public DbSet<Category> Categories { get; set; }
    public DbSet<Course> Courses { get; set; }
    public DbSet<Lesson> Lessons { get; set; }
    public DbSet<Payment> Payments { get; set; }
    public DbSet<Progress> Progresses { get; set; }
    public DbSet<Subscription> Subscriptions { get; set; }
    public DbSet<User> Users { get; set; }

    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
        : base(options) { }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.ApplyConfigurationsFromAssembly(typeof(ApplicationDbContext).Assembly);
        base.OnModelCreating(modelBuilder);
    }
}
