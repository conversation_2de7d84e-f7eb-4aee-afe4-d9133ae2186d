using Application.Abstractions.Persistent;
using Microsoft.EntityFrameworkCore.Storage;

namespace Infrastructure.Persistent.Data;

internal sealed class UnitOfWork: IUnitOfWork
{
    private readonly ApplicationDbContext _dbContext;
    private IDbContextTransaction? _transaction;

    public UnitOfWork(ApplicationDbContext dbContext) => _dbContext = dbContext;

    public int SaveChanges() => _dbContext.SaveChanges();

    public async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default) => await _dbContext.SaveChangesAsync(cancellationToken);

    public async Task BeginTransactionAsync(
        CancellationToken cancellationToken = default)
    {
        if(_transaction is not null)
        {
            throw new InvalidOperationException("Transaction already started.");
        }
        _transaction = await _dbContext.Database.BeginTransactionAsync(cancellationToken);
    }

    public async Task CommitTransactionAsync(
        CancellationToken cancellationToken = default)
    {
        if(_transaction is null)
        {
            throw new InvalidOperationException("Transaction not started.");
        }
        try
        {
            await _dbContext.SaveChangesAsync(cancellationToken);
            await _transaction.CommitAsync(cancellationToken);
        }
        catch
        {
            await _transaction.RollbackAsync(cancellationToken);
            throw;
        }
        finally
        {
            _transaction.Dispose();
            _transaction = null;
        }
    }


    public async Task RollbackTransactionAsync(CancellationToken cancellationToken = default)
    {
        if(_transaction is null)
        {
            throw new InvalidOperationException("Transaction not started.");
        }
        await _transaction.RollbackAsync(cancellationToken);
        _transaction.Dispose();
        _transaction = null;
    }
}
