using Domain.Courses;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Persistent.Configurations;

internal sealed class CourseConfiguration : IEntityTypeConfiguration<Course>
{
    public void Configure(EntityTypeBuilder<Course> builder)
    {
        builder.HasKey(e => e.Id);
        builder.Property(e => e.Id).ValueGeneratedOnAdd();

        builder.Property(e => e.CreatorId).IsRequired();
        builder.Property(e => e.Title).IsRequired().IsUnicode().HasMaxLength(500);
        builder.Property(e => e.Description).IsUnicode();
        builder.Property(e => e.CategoryId).IsRequired();
        builder.Property(e => e.CoverImageUrl).IsRequired();
        builder.Property(e => e.IsPublished).IsRequired().HasDefaultValue(false);

        builder
            .HasOne(e => e.Category)
            .WithMany(e => e.Courses)
            .HasForeignKey(e => e.CategoryId)
            .HasPrincipalKey(e => e.Id)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(e => e.Creator)
            .WithMany(e => e.Courses)
            .HasForeignKey(e => e.CreatorId)
            .HasPrincipalKey(e => e.Id)
            .OnDelete(DeleteBehavior.Cascade);

        builder
            .HasMany(e => e.Lessons)
            .WithOne(e => e.Course)
            .HasForeignKey(e => e.CourseId)
            .HasPrincipalKey(e => e.Id)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
