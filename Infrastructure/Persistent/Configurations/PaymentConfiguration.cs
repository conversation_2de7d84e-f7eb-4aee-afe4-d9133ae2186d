using Domain.Payments;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Persistent.Configurations;

internal sealed class PaymentConfiguration : IEntityTypeConfiguration<Payment>
{
    public void Configure(EntityTypeBuilder<Payment> builder)
    {
        builder.HasKey(e => e.Id);
        builder.Property(e => e.Id).ValueGeneratedOnAdd();

        builder.Property(e => e.UserId).IsRequired();
        builder.Property(e => e.SubscriptionId).IsRequired();
        builder.Property(e => e.Provider).IsRequired().HasConversion<string>();
        builder.Property(e => e.TransactionId).IsRequired();
        builder.OwnsOne(
            e => e.Price,
            nb =>
            {
                nb.Property(x => x.Amount).HasPrecision(18, 2).HasColumnName("Price");
                nb.OwnsOne(
                    x => x.Currency,
                    cb => cb.Property(y => y.Value).IsRequired().HasColumnName("Currency")
                );
            }
        );
        builder.Property(e => e.Status).IsRequired().HasConversion<string>();
        builder.Property(e => e.CreatedAt).IsRequired();

        builder
            .HasOne(e => e.User)
            .WithMany()
            .HasForeignKey(e => e.UserId)
            .HasPrincipalKey(e => e.Id)
            .OnDelete(DeleteBehavior.Cascade);
        builder.HasOne(e => e.Subscription).WithOne().OnDelete(DeleteBehavior.NoAction);
    }
}
