using Domain.Subscriptions;
using Domain.Users;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Persistent.Configurations;

internal sealed class UserConfiguration : IEntityTypeConfiguration<User>
{
    public void Configure(EntityTypeBuilder<User> builder)
    {
        builder.HasKey(e => e.Id);
        builder.Property(e => e.Id).ValueGeneratedOnAdd();
        builder.Property(e => e.ExternalUserId).IsRequired();
        builder.Property(e => e.SubscriptionId).IsRequired();

        builder
            .HasOne(e => e.Subscription)
            .WithOne()
            .HasForeignKey<Subscription>(e => e.UserId)
            .OnDelete(DeleteBehavior.NoAction);

        builder
            .HasMany(e => e.Courses)
            .WithOne(e => e.Creator)
            .HasForeignKey(e => e.CreatorId)
            .HasPrincipalKey(e => e.Id)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.Progresses).WithOne().OnDelete(DeleteBehavior.Cascade);
    }
}
