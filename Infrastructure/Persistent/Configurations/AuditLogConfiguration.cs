using Domain.AuditLogs;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Persistent.Configurations;

internal sealed class AuditLogConfiguration : IEntityTypeConfiguration<AuditLog>
{
    public void Configure(EntityTypeBuilder<AuditLog> builder)
    {
        builder.HasKey(e => e.Id);
        builder.Property(e => e.Id).ValueGeneratedOnAdd();

        builder.Property(e => e.UserId).IsRequired();
        builder.Property(e => e.Action).IsRequired().HasMaxLength(500);
        builder.Property(e => e.EntityType).IsRequired();
        builder.Property(e => e.EntityId).IsRequired();
        builder.Property(e => e.TimeStamp).IsRequired();
    }
}
