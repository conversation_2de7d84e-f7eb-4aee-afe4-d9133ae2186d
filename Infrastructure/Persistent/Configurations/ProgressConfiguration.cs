using Domain.Progresses;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Persistent.Configurations;

internal sealed class ProgressConfiguration : IEntityTypeConfiguration<Progress>
{
    public void Configure(EntityTypeBuilder<Progress> builder)
    {
        builder.HasKey(e => e.Id);
        builder.Property(e => e.Id).ValueGeneratedOnAdd();

        builder.Property(e => e.UserId).IsRequired();
        builder.Property(e => e.LessonId).IsRequired();
        builder.Property(e => e.CompletedAt).IsRequired();

        builder.HasIndex(e => new { e.UserId, e.LessonId }).IsUnique();
    }
}
