using Domain.Subscriptions;
using Domain.Users;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Persistent.Configurations;

internal sealed class SubscriptionConfiguration : IEntityTypeConfiguration<Subscription>
{
    public void Configure(EntityTypeBuilder<Subscription> builder)
    {
        builder.HasKey(e => e.Id);
        builder.Property(e => e.Id).ValueGeneratedOnAdd();

        builder.Property(e => e.UserId).IsRequired();
        builder.Property(e => e.Plan).IsRequired().HasConversion<string>();
        builder.Property(e => e.StartDate).IsRequired();
        builder.Property(e => e.EndDate);
        builder.Property(e => e.Status).IsRequired().HasConversion<string>();

        builder
            .HasOne(e => e.User)
            .WithOne(e => e.Subscription)
            .HasForeignKey<User>(e => e.SubscriptionId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
