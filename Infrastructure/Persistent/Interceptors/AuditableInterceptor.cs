using Domain.Primitives;
using Infrastructure.Extensions;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Diagnostics;

namespace Infrastructure.Persistent.Interceptors;

public sealed class AuditableInterceptor : SaveChangesInterceptor
{
    private readonly IHttpContextAccessor _contextAccessor;

    public AuditableInterceptor(IHttpContextAccessor contextAccessor) =>
        _contextAccessor = contextAccessor;

    public override InterceptionResult<int> SavingChanges(
        DbContextEventData eventData,
        InterceptionResult<int> result
    )
    {
        if (eventData.Context is null)
        {
            return base.SavingChanges(eventData, result);
        }

        IEnumerable<EntityEntry<AuditableEntity>> entriesAdd = eventData
            .Context.ChangeTracker.Entries<AuditableEntity>()
            .Where(e => e.State == EntityState.Added);

        IEnumerable<EntityEntry<AuditableEntity>> entriesUpdate = eventData
            .Context.ChangeTracker.Entries<AuditableEntity>()
            .Where(e => e.State == EntityState.Modified);

        foreach (EntityEntry<AuditableEntity> entryAdd in entriesAdd)
        {
            entryAdd.Entity.CreatedAt = DateTime.UtcNow;
            entryAdd.Entity.CreatedBy = _contextAccessor.GetUsername() ?? "anonymous";
        }

        foreach (EntityEntry<AuditableEntity> entryUpdate in entriesUpdate)
        {
            entryUpdate.Entity.UpdatedAt = DateTime.UtcNow;
            entryUpdate.Entity.UpdatedBy = _contextAccessor.GetUsername() ?? "anonymous";
        }

        return base.SavingChanges(eventData, result);
    }

    public override ValueTask<InterceptionResult<int>> SavingChangesAsync(
        DbContextEventData eventData,
        InterceptionResult<int> result,
        CancellationToken cancellationToken = default
    )
    {
        if (eventData.Context is null)
        {
            return base.SavingChangesAsync(eventData, result, cancellationToken);
        }

        IEnumerable<EntityEntry<AuditableEntity>> entriesAdd = eventData
            .Context.ChangeTracker.Entries<AuditableEntity>()
            .Where(e => e.State == EntityState.Added);

        IEnumerable<EntityEntry<AuditableEntity>> entriesUpdate = eventData
            .Context.ChangeTracker.Entries<AuditableEntity>()
            .Where(e => e.State == EntityState.Modified);

        foreach (EntityEntry<AuditableEntity> entryAdd in entriesAdd)
        {
            entryAdd.Entity.CreatedAt = DateTime.UtcNow;
            entryAdd.Entity.CreatedBy = _contextAccessor.GetUsername() ?? "anonymous";
        }

        foreach (EntityEntry<AuditableEntity> entryUpdate in entriesUpdate)
        {
            entryUpdate.Entity.UpdatedAt = DateTime.UtcNow;
            entryUpdate.Entity.UpdatedBy = _contextAccessor.GetUsername() ?? "anonymous";
        }

        return base.SavingChangesAsync(eventData, result, cancellationToken);
    }
}
