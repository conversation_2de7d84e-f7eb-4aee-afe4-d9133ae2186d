using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;
using BuildingBlocks.Application.Mail;
using Application.Common.Options;
using MailKit.Net.Smtp;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Options;
using MimeKit;
using Shared;

namespace Infrastructure.Mail;

public class EmailService : IEmailService
{
    private const string EmailConfigKey = "email";
    private readonly MailSettingsOption _option;
    private readonly IWebHostEnvironment _webHostEnvironment;

    public EmailService(IOptions<MailSettingsOption> option, IWebHostEnvironment webHostEnvironment)
    {
        _option = option.Value;
        _webHostEnvironment = webHostEnvironment;
    }

    public async Task<Result> SendAsync(string to, string subject, string body)
    {
        var fromEmail = new MailboxAddress(_option.Name, _option.Email);
        var toEmail = new MailboxAddress(to, to);
        var emailBodyBuilder = new BodyBuilder { TextBody = body };
        MimeMessage message = new()
        {
            From = { fromEmail },
            To = { toEmail },
            Subject = subject,
            Body = emailBodyBuilder.ToMessageBody(),
        };
        var mailClient = new SmtpClient();
        await mailClient.ConnectAsync(_option.Host, _option.Port, _option.UseSsl);
        await mailClient.AuthenticateAsync(_option.Email, _option.Password);
        await mailClient.SendAsync(message);
        await mailClient.DisconnectAsync(true);
        mailClient.Dispose();
        return Result.Success();
    }

    public async Task<Result> SendTemplateAsync(
        string to,
        string subject,
        string templateName,
        Dictionary<string, object> data
    )
    {
        string body = ParseTemplate(templateName, data);
        if (string.IsNullOrWhiteSpace(body))
        {
            return Result.Failure(
                Error.Validation("EmailBody.Null", "Email body should not be null")
            );
        }

        MimeMessage message = new();
        var fromEmail = new MailboxAddress(_option.Name, _option.Email);
        message.From.Add(fromEmail);
        var toEmail = new MailboxAddress(to, to);
        message.To.Add(toEmail);
        message.Subject = subject;
        var emailBodyBuilder = new BodyBuilder { HtmlBody = body };
        message.Body = emailBodyBuilder.ToMessageBody();
        var mailClient = new SmtpClient();
        await mailClient.ConnectAsync(_option.Host, _option.Port, _option.UseSsl);
        await mailClient.AuthenticateAsync(_option.Email, _option.Password);
        await mailClient.SendAsync(message);
        await mailClient.DisconnectAsync(true);
        mailClient.Dispose();
        return Result.Success();
    }

    private string ParseTemplate(string templateName, Dictionary<string, object> data)
    {
        string templatePath = $"{_webHostEnvironment.WebRootPath}/templates/{templateName}.html";
        if (!File.Exists(templatePath))
        {
            return "";
        }

        string templateContent = File.ReadAllText(templatePath);

        // Handle each loops first
        var eachRegex = new Regex(@"{{#each\s+(\w+)}}(.*?){{/each}}", RegexOptions.Singleline);
        MatchCollection matches = eachRegex.Matches(templateContent);

        foreach (Match match in matches)
        {
            string arrayName = match.Groups[1].Value;
            string template = match.Groups[2].Value;
            var result = new StringBuilder();

            if (data.TryGetValue(arrayName, out object? value))
            {
                switch (value)
                {
                    case IEnumerable<Dictionary<string, string>> dictItems:
                    {
                        foreach (Dictionary<string, string> item in dictItems)
                        {
                            string itemContent = template;
                            foreach (KeyValuePair<string, string> kvp in item)
                            {
                                itemContent = itemContent.Replace(
                                    "{{" + kvp.Key.ToLowerInvariant() + "}}",
                                    kvp.Value ?? ""
                                );
                            }

                            result.Append(itemContent);
                        }

                        break;
                    }
                    // Handle other IEnumerable types
                    case IEnumerable<object> items:
                    {
                        foreach (object item in items)
                        {
                            PropertyInfo[] itemProperties = item.GetType().GetProperties();
                            string itemContent = itemProperties.Aggregate(
                                template,
                                (current, prop) =>
                                    current.Replace(
                                        "{{" + prop.Name.ToLowerInvariant() + "}}",
                                        prop.GetValue(item)?.ToString() ?? ""
                                    )
                            );

                            result.Append(itemContent);
                        }

                        break;
                    }
                }
            }

            templateContent = templateContent.Replace(match.Value, result.ToString());
        }

        // Handle regular variables
        foreach (string key in data.Keys)
        {
            string loweredKey = key.ToLowerInvariant();
            templateContent = templateContent.Replace(
                "{{" + loweredKey + "}}",
                data[key].ToString() ?? ""
            );
        }

        return templateContent;
    }
}
