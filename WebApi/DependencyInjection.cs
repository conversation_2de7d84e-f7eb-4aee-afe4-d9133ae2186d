using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.AspNetCore.Cors;
using Microsoft.IdentityModel.Tokens;
using Serilog;
using WebAPI.Middlewares;

namespace WebApi;

public static class DependencyInjection
{
    public static IServiceCollection ConfigureLogger(
        this IServiceCollection services,
        IConfigurationManager configuration
    )
    {
        Log.Logger = new LoggerConfiguration()
            .WriteTo.Console()
            .ReadFrom.Configuration(configuration)
            .CreateLogger();

        return services.AddSerilog();
    }

    public static IServiceCollection ConfigureCors(this IServiceCollection services)
    {
        var devCors = new EnableCorsAttribute(ApiConfig.CORS_DEV);
        var prodCors = new EnableCorsAttribute(ApiConfig.CORS_PROD);
        services.AddCors(options =>
            options.AddPolicy(
                devCors.PolicyName!,
                policy => policy.AllowAnyOrigin().AllowAnyMethod().AllowAnyHeader()
            )
        );

        services.AddCors(options =>
            options.AddPolicy(
                prodCors.PolicyName!,
                policy =>
                    policy
                        .WithOrigins("https://aiotech.cloud")
                        .AllowAnyMethod()
                        .AllowAnyHeader()
                        .AllowCredentials()
            )
        );
        return services;
    }

    public static IServiceCollection ConfigureAuthentication(
        this IServiceCollection services,
        IConfiguration configuration
    )
    {
        services
            .AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = CookieAuthenticationDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme =
                    OpenIdConnectDefaults.AuthenticationScheme;
            })
            .AddCookie()
            .AddOpenIdConnect(options =>
            {
                options.Authority = Environment.GetEnvironmentVariable("ConnectionString__Auth") ?? throw new Exception("ConnectionString__Auth not found");
                options.ClientId = Environment.GetEnvironmentVariable("AUTH_CLIENT_ID") ?? throw new Exception("AUTH_CLIENT_ID not found");
                options.ClientSecret = Environment.GetEnvironmentVariable("AUTH_CLIENT_SECRET") ?? throw new Exception("AUTH_CLIENT_SECRET not found");
                options.ResponseType = "code";
                options.Scope.Add("openid");
                options.Scope.Add("profile");
                options.Scope.Add("email");
                options.SaveTokens = true;
                options.RequireHttpsMetadata = false;
    
                // Map Zitadel user ID to claim
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    NameClaimType = "sub",
                    RoleClaimType = "roles"
                };
            });
        return services;
    }

    public static IServiceCollection ConfigureAuthorization(this IServiceCollection services)
    {
        services
            .AddAuthorizationBuilder()
            .AddPolicy(ApiConfig.ADMIN_POLICY, policy => policy.RequireRole(ApiConfig.ADMIN_POLICY))
            .AddPolicy(
                ApiConfig.CREATOR_POLICY,
                policy => policy.RequireRole(ApiConfig.CREATOR_POLICY)
            );
        return services;
    }

    public static IServiceCollection ConfigureExceptionHandler(this IServiceCollection services)
    {
        services.AddExceptionHandler<GlobalExceptionHandler>();
        services.AddProblemDetails();
        return services;
    }
}
