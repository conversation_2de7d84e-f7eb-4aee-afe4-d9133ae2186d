using Application;
using FastEndpoints;
using FastEndpoints.Swagger;
using Infrastructure;
using Scalar.AspNetCore;
using Serilog;
using Shared;
using WebApi;
using WebApi.Extensions;
using ProblemDetails = Microsoft.AspNetCore.Mvc.ProblemDetails;

WebApplicationBuilder builder = WebApplication.CreateBuilder(args);

// Add services to the container.
// Learn more about configuring OpenAPI at https://aka.ms/aspnet/openapi
ConfigurationManager configuration = builder.Configuration;
builder.Services.AddHttpContextAccessor();
builder.Services.AddInfrastructure(configuration).AddApplication(configuration)
       .ConfigureLogger(configuration).ConfigureCors()
       .ConfigureAuthentication(configuration).ConfigureAuthorization()
       .ConfigureExceptionHandler();

builder.Services.AddFastEndpoints().AddResponseCaching().SwaggerDocument(o =>
{
    o.DocumentSettings = s =>
    {
        s.Title = "Learnify Api";
        s.DocumentName = "v1";
        s.Version = "v1";
    };
    o.RemoveEmptyRequestSchema = true;
    o.ExcludeNonFastEndpoints = true;
    o.AutoTagPathSegmentIndex = 0;
});

WebApplication app = builder.Build();

Log.Information("Starting up api");

// Configure the HTTP request pipeline.
if (app.Environment.IsProduction())
{
    app.UseExceptionHandler();
}

app.UseHsts();
app.UseHttpsRedirection();

app.UseCors(app.Environment.IsProduction()
    ? ApiConfig.CORS_PROD
    : ApiConfig.CORS_DEV);

app.UseAuthentication();
app.UseAuthorization();

app.UseResponseCaching().UseFastEndpoints(fe =>
{
    fe.Endpoints.RoutePrefix = "api";
    fe.Errors.ResponseBuilder = (failures,_,statusCode) =>
    {
        var errors = failures
                     .Select(f =>
                         Error.Validation($"validation_{f.PropertyName}",
                             f.ErrorMessage)).Select(error => new
                     {
                         error.Code,error.Description,
                         Type = error.Type.ToString(),
                     }).ToList();
        var problemDetails = new ProblemDetails
        {
            Status = statusCode,
            Title = "One or more validation errors occurred.",
            Type = "https://tools.ietf.org/html/rfc7231#section-6.5.1",
            Extensions =
            {
                ["errors"] = errors,
            },
        };
        return problemDetails;
    };
});

Log.Information("Api has started up successfully");

app.RunMigration();

if (app.Environment.IsDevelopment())
{
    app.UseOpenApi(c => c.Path = "/openapi/{documentName}.json");
    app.MapScalarApiReference(o =>
        o.WithTheme(ScalarTheme.DeepSpace).WithModels(false).WithSidebar(false)
         .WithDarkModeToggle().WithTitle("Learnify Api Documentation"));
}

app.Run();

Log.Information("Shutting down api");