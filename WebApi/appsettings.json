{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "MailSettings": {"Host": "", "Port": 0, "Email": "", "Password": "", "DefaultCredentials": true, "Name": "Learnify", "UseSsl": true}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.Grafana.Loki"], "MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Warning", "Microsoft.AspNetCore": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.EntityFrameworkCore.Database": "Information", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz}] [{Level:u3}] {Message}{NewLine}{Exception}"}}, {"Name": "GrafanaLoki", "Args": {"uri": "http://loki:3100", "labels": [{"key": "app", "value": "web_app"}], "propertiesAsLabels": ["app"], "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss} [{Level:u3}] {Message}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithCorrelationId", "WithMachineName", "WithExceptionDetails"]}}