using Infrastructure.Persistent.Data;
using Microsoft.EntityFrameworkCore;
using Serilog;

namespace WebApi.Extensions;

public static class WebApplicationBuilderExtension
{
    public static WebApplication RunMigration(this WebApplication app)
    {
        if (!app.Environment.IsDevelopment())
        {
            return app;
        }

        IServiceScope scope = app.Services.CreateScope();
        ApplicationDbContext? context = scope.ServiceProvider.GetService<ApplicationDbContext>();
        if (context is null || !context.Database.GetPendingMigrations().Any())
        {
            return app;
        }

        Log.Information("Starting migration");
        context.Database.Migrate();
        Log.Information("Migration completed");
        return app;
    }
}
