using Application.Categories.Dto;
using Application.Categories.GetAll;
using Application.Categories.GetById;
using Application.Common.Models;
using FastEndpoints;
using Orchestrix.Mediator;
using Shared;
using WebAPI.Extensions;

namespace WebApi.Endpoints.Categories;

internal sealed class GetCategoryByIdEndpoint : Endpoint<GetCategoryByIdQuery, CategoryDto>
{
    private readonly ISender _sender;

    public GetCategoryByIdEndpoint(ISender sender)
    {
        _sender = sender;
    }


    public override void Configure()
    {
        Get("/{id:guid}");
        Group<CategoryGroupEndpoint>();
    }
    
    public override async Task HandleAsync(GetCategoryByIdQuery req, CancellationToken ct)
    {
        Result<CategoryDto> result = await _sender.Send(req, ct);
        if (result.IsFailure)
        {
            await Send.ResultAsync(result.Error.ToProblemDetails());
        }
        await Send.OkAsync(result.Value, ct);
    }
}
