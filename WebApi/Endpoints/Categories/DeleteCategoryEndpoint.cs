using Application.Categories.Delete;
using FastEndpoints;
using Orchestrix.Mediator;
using Shared;
using WebAPI.Extensions;

namespace WebApi.Endpoints.Categories;

internal sealed class DeleteCategoryEndpoint : Endpoint<DeleteCategoryCommand>
{
    private readonly ISender _sender;
    
    public DeleteCategoryEndpoint(ISender sender)
    {
        _sender = sender;
    }
    
    public override void Configure()
    {
        Delete("/{id:guid}");
        Group<CategoryGroupEndpoint>();
    }

    public override async Task HandleAsync(DeleteCategoryCommand req,CancellationToken ct)
    {
        Result result = await _sender.Send(req,ct);
        if (result.IsFailure)
        {
            await Send.ResultAsync(result.Error.ToProblemDetails());
        }
        await Send.OkAsync(cancellation: ct);
    }
}
