using Application.Categories.Update;
using FastEndpoints;
using Orchestrix.Mediator;
using Shared;
using WebAPI.Extensions;

namespace WebApi.Endpoints.Categories;

internal sealed class UpdateCategoryEndpoint : Endpoint<UpdateCategoryCommand, Guid>
{
    private readonly ISender _sender;
    public UpdateCategoryEndpoint(ISender sender)
    {
        _sender = sender;
    }
    
    public override void Configure()
    {
        Put("/{id:guid}");
        Group<CategoryGroupEndpoint>();
    }
    
    public override async Task HandleAsync(UpdateCategoryCommand req, CancellationToken ct)
    {
        Result<Guid> result = await _sender.Send(req, ct);
        if (result.IsFailure)
        {
            await Send.ResultAsync(result.Error.ToProblemDetails());
        }
        await Send.OkAsync(result.Value, ct);
    }
}
