using Application.Categories.Create;
using FastEndpoints;
using Orchestrix.Mediator;
using Shared;
using WebAPI.Extensions;

namespace WebApi.Endpoints.Categories;

internal sealed class CreateCategoryEndpoint : Endpoint<CreateCategoryCommand, Guid>
{
    private readonly ISender _sender;

    public CreateCategoryEndpoint(ISender sender)
    {
        _sender = sender;
    }


    public override void Configure()
    {
        Post("/");
        Group<CategoryGroupEndpoint>();
    }
    
    public override async Task HandleAsync(CreateCategoryCommand req, CancellationToken ct)
    {
        Result<Guid> result = await _sender.Send(req, ct);
        if (result.IsFailure)
        {
            await Send.ResultAsync(result.Error.ToProblemDetails());
        }
        await Send.OkAsync(result.Value, ct);
    }
}
