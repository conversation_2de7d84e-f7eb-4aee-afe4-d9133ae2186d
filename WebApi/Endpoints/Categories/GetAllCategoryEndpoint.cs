using Application.Categories.Dto;
using Application.Categories.GetAll;
using Application.Common.Models;
using FastEndpoints;
using Orchestrix.Mediator;
using Shared;
using WebAPI.Extensions;

namespace WebApi.Endpoints.Categories;

internal sealed class GetAllCategoryEndpoint : Endpoint<GetAllCategoryQuery, PagedList<CategoryDto>>
{
    private readonly ISender _sender;

    public GetAllCategoryEndpoint(ISender sender)
    {
        _sender = sender;
    }


    public override void Configure()
    {
        Get("/");
        Group<CategoryGroupEndpoint>();
    }
    
    public override async Task HandleAsync(GetAllCategoryQuery req, CancellationToken ct)
    {
        Result<PagedList<CategoryDto>> result = await _sender.Send(req, ct);
        if (result.IsFailure)
        {
            await Send.ResultAsync(result.Error.ToProblemDetails());
        }
        await Send.OkAsync(result.Value, ct);
    }
}
