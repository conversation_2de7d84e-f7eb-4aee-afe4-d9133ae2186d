<Project>
  <PropertyGroup>
    <!-- Enable central package management, https://learn.microsoft.com/en-us/nuget/consume-packages/Central-Package-Management -->
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
  </PropertyGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup>
    <PackageVersion Include="EFCore.NamingConventions" Version="9.0.0" />
    <PackageVersion Include="FastEndpoints" Version="7.0.1" />
    <PackageVersion Include="FastEndpoints.Swagger" Version="7.0.1" />
    <PackageVersion Include="FluentValidation" Version="12.0.0" />
    <PackageVersion Include="MailKit" Version="4.13.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="9.0.9" />
    <PackageVersion Include="Microsoft.AspNetCore.OpenApi" Version="9.0.9" />
    <PackageVersion Include="Microsoft.AspNetCore.SignalR.Client" Version="9.0.9" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore" Version="9.0.9" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.9" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Relational" Version="9.0.9" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.9" />
    <PackageVersion Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="9.0.9" />
    <PackageVersion Include="Microsoft.Extensions.Http.Resilience" Version="9.9.0" />
    <PackageVersion Include="Microsoft.Extensions.Logging" Version="9.0.9" />
    <PackageVersion Include="Minio" Version="6.0.5" />
    <PackageVersion Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageVersion Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
    <PackageVersion Include="Orchestrix.Mediator" Version="1.0.0" />
    <PackageVersion Include="Orchestrix.Mediator.SourceGenerators" Version="1.0.0" />
    <PackageVersion Include="Polly.Core" Version="8.6.3" />
    <PackageVersion Include="Polly.Extensions" Version="8.6.3" />
    <PackageVersion Include="QuestPDF" Version="2025.7.1" />
    <PackageVersion Include="RestSharp" Version="112.1.0" />
    <PackageVersion Include="Scalar.AspNetCore" Version="2.8.1" />
    <PackageVersion Include="Serilog" Version="4.3.0" />
    <PackageVersion Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageVersion Include="Serilog.Sinks.Grafana.Loki" Version="8.3.1" />
  </ItemGroup>
</Project>