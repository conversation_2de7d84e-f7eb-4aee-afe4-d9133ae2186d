dotnet ef migrations add --project Infrastructure\Infrastructure.csproj --startup-project Presentation\Presentation.csproj --context Infrastructure.Persistent.Data.ApplicationDbContext --configuration Debug --output-dir Persistent.Data.Migrations

dotnet ef migrations remove --project Infrastructure\Infrastructure.csproj --startup-project Presentation\Presentation.csproj --context Infrastructure.Persistent.Data.ApplicationDbContext --configuration Debug

Update Database:
dotnet ef database update --project Infrastructure\Infrastructure.csproj --startup-project WebApi\WebApi.csproj --context Infrastructure.Persistent.ApplicationDbContext --configuration Debug

dotnet ef database update --project Infrastructure\Infrastructure.csproj --startup-project WebApi\WebApi.csproj --context Infrastructure.Persistent.ApplicationDbContext --configuration Debug [MigrationName]