using Domain.Primitives;
using Domain.Subscriptions.Enums;
using Domain.Users;

namespace Domain.Subscriptions;

public class Subscription : AuditableEntity, ISoftDeletableEntity
{
    public Guid UserId { get; private set; }
    public SubscriptionPlan Plan { get; private set; }
    public DateTime StartDate { get; private set; }
    public DateTime? EndDate { get; private set; }
    public SubscriptionStatus Status { get; private set; }

    // Soft delete
    public DateTime? DeletedAt { get; set; }
    public string? DeletedBy { get; set; }

    // Navigation Properties
    public virtual User User { get; } = null!;

    private Subscription() { } // For EF Core

    public Subscription(Guid userId, SubscriptionPlan plan, DateTime startDate, DateTime? endDate)
    {
        UserId = userId;
        Plan = plan;
        StartDate = startDate;
        EndDate = endDate;
        Status = SubscriptionStatus.Active;
    }

    public void SetExpirationDate(DateTime? endDate) => EndDate = endDate;

    public void SetStatus(SubscriptionStatus status) => Status = status;
}
