using Domain.Categories;
using Domain.Lessons;
using Domain.Primitives;
using Domain.Users;

namespace Domain.Courses;

public class Course : AuditableEntity, ISoftDeletableEntity
{
    public Guid CreatorId { get; private set; }
    public string Title { get; private set; }
    public string? Description { get; private set; }
    public Guid CategoryId { get; private set; }
    public string CoverImageUrl { get; private set; }
    public bool IsPublished { get; private set; }

    // Soft delete
    public DateTime? DeletedAt { get; set; }
    public string? DeletedBy { get; set; }

    // Navigation Properties
    public virtual Category Category { get; } = null!;
    public virtual User Creator { get; } = null!;
    public virtual ICollection<Lesson> Lessons { get; } = [];

    private Course() { } // For EF Core

    public Course(Guid creatorId, string title, string? description, Guid categoryId)
    {
        CreatorId = creatorId;
        Title = title;
        Description = description;
        CategoryId = categoryId;
        IsPublished = false;
    }

    public Course(
        Guid creatorId,
        string title,
        string? description,
        Guid categoryId,
        bool isPublished
    )
    {
        CreatorId = creatorId;
        Title = title;
        Description = description;
        CategoryId = categoryId;
        IsPublished = isPublished;
    }

    public void Update(string title, string? description, Guid categoryId) =>
        (Title, Description, CategoryId) = (title, description, categoryId);

    public void SetCoverImageUrl(string coverImageUrl) => CoverImageUrl = coverImageUrl;
}
