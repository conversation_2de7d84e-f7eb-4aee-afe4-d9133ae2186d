using Domain.Primitives;

namespace BuildingBlocks.Domain.ValueObjects;

public sealed class PhoneNumber : ValueObject
{
    public string Value { get; }

    private PhoneNumber()
    {
    } // For EF Core

    private PhoneNumber(string value) => Value = value;

    public static PhoneNumber Create(string value)
    {
        if (string.IsNullOrWhiteSpace(value))
        {
            throw new ArgumentException("Phone number cannot be empty");
        }

        string cleaned = value.Replace(" ", "").Replace("-", "");
        if (cleaned.Length < 10)
        {
            throw new DomainException(
                "Phone number must be at least 10 digits");
        }

        return new PhoneNumber(cleaned);
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Value;
    }
}
