using Domain.Primitives;

namespace BuildingBlocks.Domain.ValueObjects;

public sealed class Money : ValueObject
{
    public decimal Amount { get; }
    public Currency Currency { get; }

    private Money() { } // For EF Core

    private Money(decimal amount, Currency currency)
    {
        if (amount < 0)
        {
            throw new DomainException("Amount cannot be negative");
        }

        Amount = amount;
        Currency = currency ??
            throw new ArgumentNullException(nameof(currency));
    }

    public static Money Create(decimal amount, Currency currency) =>
        new(amount, currency);

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Amount;
        yield return Currency;
    }
}
