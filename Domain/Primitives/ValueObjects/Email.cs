using Domain.Primitives;

namespace BuildingBlocks.Domain.ValueObjects;

public sealed class Email : ValueObject
{
    public string Value { get; }

    private Email()
    {
    } // For EF Core

    private Email(string value) => Value = value;

    public static Email Create(string email)
    {
        if (string.IsNullOrWhiteSpace(email))
        {
            throw new ArgumentException("Email cannot be empty");
        }

        if (!IsValidEmail(email))
        {
            throw new DomainException("Invalid email");
        }

        return new Email(email.ToLowerInvariant());
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Value;
    }

    private static bool IsValidEmail(string email) =>
        email.Contains('@') && email.Contains('.');
}
