using Domain.Primitives;

namespace BuildingBlocks.Domain.ValueObjects;

public class Language : ValueObject
{
    public static List<Language> All =
        [English, Vietnamese, Chinese, Japanese, Korean,];

    public string Code { get; } = null!;
    public string EnglishName { get; } = null!;
    public bool IsRtl { get; }

    public static Language English => new("en", "English");
    public static Language Vietnamese => new("vi", "Vietnamese");
    public static Language Chinese => new("zh", "Chinese");
    public static Language Japanese => new("ja", "Japanese");
    public static Language Korean => new("ko", "Korean");

    private Language(string code, string englishName, bool isRtl = false)
    {
        if (!IsValid(code, englishName))
        {
            throw new ArgumentException("Invalid language");
        }

        Code = code;
        EnglishName = englishName;
        IsRtl = isRtl;
    }

    public static Language Create(string code,
                                  string englishName,
                                  bool isRtl = false) => new(code, englishName,
        isRtl);

    public static Language FromCode(string code) =>
        All.FirstOrDefault(x => x.Code == code) ?? English;

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Code;
        yield return EnglishName;
        yield return IsRtl;
    }

    private bool IsValid(string code, string englishName) =>
        !string.IsNullOrWhiteSpace(code) &&
        !string.IsNullOrWhiteSpace(englishName);
}
