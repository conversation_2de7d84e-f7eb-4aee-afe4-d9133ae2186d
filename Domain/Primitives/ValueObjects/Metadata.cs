using Domain.Primitives;

namespace BuildingBlocks.Domain.ValueObjects;

public sealed class Metadata : ValueObject
{
    public string Title { get; }
    public string Description { get; }
    public string Keywords { get; }

    private Metadata()
    {
    } // For EF Core

    private Metadata(string title, string description, string keywords)
    {
        Title = title;
        Description = description;
        Keywords = keywords;
    }

    public static Metadata Create(string title,
                                  string description,
                                  string keywords)
    {
        if (string.IsNullOrWhiteSpace(title))
        {
            throw new ArgumentException("Title cannot be empty");
        }

        if (string.IsNullOrWhiteSpace(description))
        {
            throw new ArgumentException("Description cannot be empty");
        }

        if (string.IsNullOrWhiteSpace(keywords))
        {
            throw new ArgumentException("Keywords cannot be empty");
        }

        return new Metadata(title, description, keywords);
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Title;
        yield return Description;
        yield return Keywords;
    }
}
