using Domain.Primitives;

namespace BuildingBlocks.Domain.ValueObjects;

public sealed class Currency : ValueObject
{
    public static readonly Currency Usd = new("USD");
    public static readonly Currency Eur = new("EUR");
    public static readonly Currency Vnd = new("VND");
    public static readonly Currency Jpy = new("JPY");

    public static readonly IReadOnlyCollection<Currency> All =
        [Usd, Eur, Vnd, Jpy,];

    public string Value { get; }

    private Currency()
    {
    } // For EF Core

    private Currency(string value) => Value = value;

    public static Currency FromCode(string value) =>
        All.FirstOrDefault(x => x.Value == value) ??
        throw new DomainException("Unsupported currency");

    public override string ToString() => Value;

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Value;
    }
}
