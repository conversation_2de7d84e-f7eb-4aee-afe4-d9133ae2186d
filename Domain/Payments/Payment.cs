using Domain.Payments.Enums;
using Domain.Primitives;
using BuildingBlocks.Domain.ValueObjects;
using Domain.Subscriptions;
using Domain.Users;

namespace Domain.Payments;

public class Payment : BaseEntity
{
    public Guid UserId { get; private set; }
    public Guid SubscriptionId { get; private set; }
    public PaymentProvider Provider { get; private set; }
    public string TransactionId { get; private set; }
    public Money Price { get; private set; }
    public PaymentStatus Status { get; private set; }
    public DateTime CreatedAt { get; private set; }

    // Navigation property
    public virtual User User { get; } = null!;
    public virtual Subscription Subscription { get; } = null!;

    private Payment() { } // For EF Core

    public Payment(
        Guid userId,
        Guid subscriptionId,
        PaymentProvider provider,
        string transactionId,
        Money price,
        DateTime createdAt
    )
    {
        UserId = userId;
        SubscriptionId = subscriptionId;
        Provider = provider;
        TransactionId = transactionId;
        Price = price;
        Status = PaymentStatus.Pending;
        CreatedAt = createdAt;
    }

    public void SetStatus(PaymentStatus status) => Status = status;
}
