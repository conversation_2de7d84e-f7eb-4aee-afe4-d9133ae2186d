using Domain.Primitives;

namespace Domain.Progresses;

public class Progress : BaseEntity
{
    public Guid UserId { get; }
    public Guid LessonId { get; }
    public DateTime CompletedAt { get; }

    private Progress() { } // For EF Core

    public Progress(Guid userId, Guid lessonId)
    {
        UserId = userId;
        LessonId = lessonId;
        CompletedAt = DateTime.UtcNow;
    }

    public Progress(Guid userId, Guid lessonId, DateTime completedAt)
    {
        UserId = userId;
        LessonId = lessonId;
        CompletedAt = completedAt;
    }
}
