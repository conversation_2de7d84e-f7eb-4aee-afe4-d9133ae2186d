using Domain.Courses;
using Domain.Primitives;
using Domain.Progresses;
using Domain.Subscriptions;

namespace Domain.Users;

public class User : AuditableEntity
{
    public string ExternalUserId { get; private set; } = null!;
    public Guid SubscriptionId { get; private set; }

    // Navigation Properties
    public virtual Subscription Subscription { get; } = null!;
    public virtual ICollection<Course> Courses { get; } = [];
    public virtual ICollection<Progress> Progresses { get; } = [];

    private User() { } // For EF Core

    public User(string externalUserId,Guid subscriptionId)
    {
        ExternalUserId = externalUserId;
        SubscriptionId = subscriptionId;
    }
    
    public void Update(Guid subscriptionId) => SubscriptionId = subscriptionId;
}
