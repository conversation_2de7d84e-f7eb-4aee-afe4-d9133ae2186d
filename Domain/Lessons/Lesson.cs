using Domain.Courses;
using Domain.Primitives;

namespace Domain.Lessons;

public class Lesson : BaseEntity
{
    public Guid CourseId { get; private set; }
    public string Title { get; private set; }
    public string VideoUrl { get; private set; }
    public int Duration { get; private set; }
    public int Order { get; private set; }
    public DateTime CreatedAt { get; private set; }

    // Navigation Properties
    public Course Course { get; } = null!;

    private Lesson() { } // For EF Core

    public Lesson(Guid courseId, string title, string videoUrl, int duration, int order)
    {
        CourseId = courseId;
        Title = title;
        CreatedAt = DateTime.UtcNow;
        VideoUrl = videoUrl;
        Duration = duration;
        Order = order;
    }

    public Lesson(
        Guid courseId,
        string title,
        string videoUrl,
        int duration,
        int order,
        DateTime createdAt
    )
    {
        CourseId = courseId;
        Title = title;
        VideoUrl = videoUrl;
        Duration = duration;
        Order = order;
        CreatedAt = createdAt;
    }

    public void Update(string title, string videoUrl, int duration, int order) =>
        (Title, VideoUrl, Duration, Order) = (title, videoUrl, duration, order);

    public void SetVideo(string videoUrl, int duration) =>
        (VideoUrl, Duration) = (videoUrl, duration);
}
