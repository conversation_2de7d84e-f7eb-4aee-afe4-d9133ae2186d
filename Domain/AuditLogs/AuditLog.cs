using Domain.Primitives;

namespace Domain.AuditLogs;

public class AuditLog : BaseEntity
{
    public Guid UserId { get; private set; }
    public string Action { get; private set; }
    public string EntityType { get; private set; }
    public Guid EntityId { get; private set; }
    public DateTime TimeStamp { get; private set; }

    private AuditLog() { } // For EF Core

    public AuditLog(
        Guid userId,
        string action,
        string entityType,
        Guid entityId,
        DateTime timeStamp
    )
    {
        UserId = userId;
        Action = action;
        EntityType = entityType;
        EntityId = entityId;
        TimeStamp = timeStamp;
    }
}
