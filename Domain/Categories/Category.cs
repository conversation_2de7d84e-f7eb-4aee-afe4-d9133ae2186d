using Domain.Courses;
using Domain.Primitives;

namespace Domain.Categories;

public class Category : BaseEntity
{
    public string Name { get; private set; }
    public string? Description { get; private set; }
    public int Order { get; private set; }

    public virtual ICollection<Course> Courses { get; } = [];

    private Category() { } // For EF Core

    public Category(string name, string? description)
    {
        Name = name;
        Description = description;
    }

    public Category(string name, string? description, int order)
    {
        Name = name;
        Description = description;
        Order = order;
    }

    public void Update(string name, string? description, int order) =>
        (Name, Description, Order) = (name, description, order);
}
