<Solution>
  <Configurations>
    <Platform Name="Any CPU" />
    <Platform Name="x64" />
    <Platform Name="x86" />
  </Configurations>
  <Folder Name="/Solution Items/">
    <File Path="../docker-compose.dev.yaml" />
    <File Path=".editorconfig" />
    <File Path="Directory.Packages.props" />
    <File Path="..\.env" />
  </Folder>
  <Project Path="Application/Application.csproj" />
  <Project Path="Domain/Domain.csproj" />
  <Project Path="Infrastructure/Infrastructure.csproj" />
  <Project Path="Shared/Shared.csproj" />
  <Project Path="WebApi/WebApi.csproj" />
</Solution>