using System.Diagnostics.CodeAnalysis;

namespace Shared;

public class Result
{
    protected internal Result(bool isSuccess, Error error)
    {
        if (isSuccess && error != Error.None || !isSuccess && error == Error.None)
        {
            throw new ArgumentException("Invalid error", nameof(error));
        }

        IsSuccess = isSuccess;
        Error = error;
    }

    public bool IsSuccess { get; }

    public bool IsFailure => !IsSuccess;

    public Error Error { get; }

    public static Result Success() => new(true, Error.None);

    public static Result Failure(Error error) => new(false, error);

    public static implicit operator Result(Error error) => Failure(error);
}

public class Result<TValue> : Result
{
    private readonly TValue? _value;

    protected internal Result(TValue? value, bool isSuccess, Error error)
        : base(isSuccess, error) => _value = value;

    [NotNull]
    public TValue Value =>
        IsSuccess
            ? _value!
            : throw new InvalidOperationException(
                "The value of a failure result can't be accessed."
            );

    public static Result<TValue> Success(TValue value) => new(value, true, Error.None);

    public static new Result<TValue> Failure(Error error) => new(default, false, error);

    public static implicit operator Result<TValue>(TValue? value) =>
        value is not null ? Success(value) : Failure(Error.NullValue);

    public static implicit operator Result<TValue>(Error error) => Failure(error);
}

public static class ResultExtensions
{
    public static TReturn Match<TReturn>(
        this Result result,
        Func<TReturn> success,
        Func<Error, TReturn> failure
    ) => result.IsSuccess ? success() : failure(result.Error);

    public static void Match(this Result result, Action success, Action<Error> failure)
    {
        if (result.IsSuccess)
        {
            success();
        }
        else
        {
            failure(result.Error);
        }
    }

    public static Task MatchAsync(
        this Result result,
        Func<Task> success,
        Func<Error, Task> failure
    ) => result.IsSuccess ? success() : failure(result.Error);

    public static TReturn Match<T, TReturn>(
        this Result<T> result,
        Func<T, TReturn> success,
        Func<Error, TReturn> failure
    ) => result.IsSuccess ? success(result.Value) : failure(result.Error);

    public static void Match<T>(this Result<T> result, Action<T> success, Action<Error> failure)
    {
        if (result.IsSuccess)
        {
            success(result.Value);
        }
        else
        {
            failure(result.Error);
        }
    }

    public static Task MatchAsync<T>(
        this Result<T> result,
        Func<T, Task> success,
        Func<Error, Task> failure
    ) => result.IsSuccess ? success(result.Value) : failure(result.Error);

    public static Result Bind(this Result result, Func<Result> bind) =>
        result.IsSuccess ? bind() : result;

    public static Result<TReturn> Bind<T, TReturn>(
        this Result<T> result,
        Func<T, Result<TReturn>> bind
    ) => result.IsSuccess ? bind(result.Value) : Result<TReturn>.Failure(result.Error);

    public static Result Bind<T>(this Result<T> result, Func<T, Result> bind) =>
        result.IsSuccess ? bind(result.Value) : result;

    public static Result Map(this Result result, Func<Result> map) =>
        result.IsSuccess ? map() : result;

    public static Result<T> Map<T>(this Result result, Func<T> map) =>
        result.IsSuccess ? Result<T>.Success(map()) : Result<T>.Failure(result.Error);
}
