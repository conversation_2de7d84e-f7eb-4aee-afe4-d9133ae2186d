using Application.Abstractions.Persistent;
using Domain.Categories;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Application.Categories.Update;

internal sealed class UpdateCategoryHandler : ICommandHandler<UpdateCategoryCommand, Guid>
{
    private readonly IApplicationDbContext _dbContext;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<UpdateCategoryHandler> _logger;

    public UpdateCategoryHandler(
        IApplicationDbContext dbContext,
        IUnitOfWork unitOfWork,
        ILogger<UpdateCategoryHandler> logger
    )
    {
        _dbContext = dbContext;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async ValueTask<Result<Guid>> Handle(
        UpdateCategoryCommand request,
        CancellationToken cancellationToken
    )
    {
        Category? entity = await _dbContext.Categories.FirstOrDefaultAsync(
            x => x.Id == request.Id,
            cancellationToken: cancellationToken
        );
        if (entity is null)
        {
            return Error.NotFound("Category.NotFound", "Category not found");
        }
        _logger.LogInformation("Attempting to update category {Name}", entity.Name);
        entity.Update(request.Name, request.Description, request.Order);
        _dbContext.Categories.Update(entity);
        int rows = await _unitOfWork.SaveChangesAsync(cancellationToken);
        if (rows < 1)
        {
            return Error.Failure("Database.Save", "Failed to save category");
        }
        _logger.LogInformation("Category {Name} updated", entity.Name);
        return entity.Id;
    }
}
