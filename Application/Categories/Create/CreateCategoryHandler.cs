using Application.Abstractions.Persistent;
using Domain.Categories;
using Microsoft.Extensions.Logging;

namespace Application.Categories.Create;

internal sealed class CreateCategoryHandler : ICommandHandler<CreateCategoryCommand, Guid>
{
    private readonly IApplicationDbContext _dbContext;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<CreateCategoryHandler> _logger;

    public CreateCategoryHandler(
        IApplicationDbContext dbContext,
        IUnitOfWork unitOfWork,
        ILogger<CreateCategoryHandler> logger
    )
    {
        _dbContext = dbContext;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async ValueTask<Result<Guid>> Handle(
        CreateCategoryCommand request,
        CancellationToken cancellationToken
    )
    {
        var entity = new Category(request.Name, request.Description, request.Order);
        _logger.LogInformation("Attempting to create category {Name}", entity.Name);
        _dbContext.Categories.Add(entity);
        int rows = await _unitOfWork.SaveChangesAsync(cancellationToken);
        if (rows < 1)
        {
            return Error.Failure("Database.Save", "Failed to save category");
        }
        _logger.LogInformation("Category {Name} created", entity.Name);
        return entity.Id;
    }
}
