namespace Application.Categories.Create;

public sealed class CreateCategoryCommand : ICommand<Guid>
{
    public string Name { get; set; } = null!;
    public string? Description { get; set; }
    public int Order { get; set; }
}

internal sealed class CreateCategoryCommandValidator : AbstractValidator<CreateCategoryCommand>
{
    public CreateCategoryCommandValidator()
    {
        /*
        builder.Property(e => e.Name).IsRequired().IsUnicode().HasMaxLength(255);
        builder.Property(e => e.Description).IsUnicode();
        builder.Property(e => e.Order).IsRequired().HasDefaultValue(0);
        */
        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage("Name is required.")
            .MaximumLength(255)
            .WithMessage("Name must be less than 255 characters.");
        RuleFor(x => x.Description);
        RuleFor(x => x.Order)
            .GreaterThanOrEqualTo(0)
            .WithMessage("Order must be greater than or equal to 0.");
    }
}
