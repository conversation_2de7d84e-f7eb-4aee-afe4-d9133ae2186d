using Application.Abstractions.Persistent;
using Application.Categories.Dto;
using Application.Common.Models;
using Domain.Categories;

namespace Application.Categories.GetAll;

internal sealed class GetAllCategoryHandler : IQueryHandler<GetAllCategoryQuery, PagedList<CategoryDto>>
{
    private readonly IApplicationDbContext _dbContext;
    public GetAllCategoryHandler(IApplicationDbContext dbContext) => _dbContext = dbContext;

    public async ValueTask<Result<PagedList<CategoryDto>>> <PERSON>le(
        GetAllCategoryQuery request,
        CancellationToken cancellationToken)
    {
        IQueryable<Category> query = _dbContext.Categories.AsQueryable();
        if (request.Filters is not null)
        {
            query = request.Filters.Aggregate(query,(current,filter) => current.FilterBy(filter.Field,filter.Value));
        }

        if (request.Sorts is not null)
        {
            query = request.Sorts.Aggregate(query,(current,sort) => current.SortBy(sort.Field,sort.Order));
        }

        IQueryable<CategoryDto> dtoQuery = query.ProjectToDto();
        return await PagedList<CategoryDto>.CreateAsync(dtoQuery,request.Page,
            request.PageSize, cancellationToken: cancellationToken);
    }
}