using System.Linq.Expressions;
using Domain.Categories;

namespace Application.Categories;

public static class CategoryQuery
{
    public static IQueryable<Category> FilterBy(this IQueryable<Category> query,
        string field,
        string value)
    {
        if (string.IsNullOrWhiteSpace(field) ||
            string.IsNullOrWhiteSpace(value))
        {
            return query;
        }
        
        string normalizeValue = value.ToLower();

        IQueryable<Category> queryExpression = field switch
        {
            "name" => query.Where(x => x.Name.ToLower() == normalizeValue),
            _ => query,
        };

        return queryExpression;
    }

    public static IQueryable<Category> SortBy(this IQueryable<Category> query,
        string field,
        string order)
    {
        if (string.IsNullOrWhiteSpace(field) ||
            string.IsNullOrWhiteSpace(order))
        {
            return query;
        }

        Expression<Func<Category, object?>> keySelector = field switch
        {
            "id" => x => x.Id,
            "name" => x => x.Name,
            "order" => x => x.Order,
            _ => x => null,
        };

        if (keySelector.Body is ConstantExpression { Value: null, })
        {
            return query;
        }

        return order.ToLowerInvariant() switch
        {
            "asc" => query.OrderBy(keySelector),
            "desc" => query.OrderByDescending(keySelector),
            _ => query,
        };
    }
}