using Application.Categories.Dto;
using Domain.Categories;

namespace Application.Categories;

public static class CategoryMapper
{
    public static CategoryDto ToDto(this Category model) =>
        new(model.Id,model.Name,model.Description,model.Order);

    public static IQueryable<CategoryDto>
        ProjectToDto(this IQueryable<Category> query) =>
        query.Select(x => new CategoryDto(x.Id, x.Name, x.Description, x.Order));
}