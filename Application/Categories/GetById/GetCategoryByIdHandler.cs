
using Application.Abstractions.Persistent;
using Application.Categories.Dto;
using Domain.Categories;
using Microsoft.EntityFrameworkCore;

namespace Application.Categories.GetById;

internal sealed class GetCategoryByIdHandler : IQueryHandler<GetCategoryByIdQuery, CategoryDto>
{
    private readonly IApplicationDbContext _dbContext;
    public GetCategoryByIdHandler(IApplicationDbContext dbContext) => _dbContext = dbContext;

    public async ValueTask<Result<CategoryDto>> Handle(
        GetCategoryByIdQuery request,
        CancellationToken cancellationToken)
    {
        CategoryDto? entity = await _dbContext.Categories
                                              .Where(x => x.Id == request.Id)
                                              .ProjectToDto()
                                              .FirstOrDefaultAsync(cancellationToken: cancellationToken);
        return entity is null ? Error.NotFound("Category.NotFound", "Category not found") : entity;
    }
}