using Application.Abstractions.Persistent;
using Domain.Categories;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Application.Categories.Delete;

internal sealed class DeleteCategoryHandler : ICommandHandler<DeleteCategoryCommand>
{
    private readonly IApplicationDbContext _dbContext;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<DeleteCategoryHandler> _logger;

    public DeleteCategoryHandler(
        IApplicationDbContext dbContext,
        IUnitOfWork unitOfWork,
        ILogger<DeleteCategoryHandler> logger
    )
    {
        _dbContext = dbContext;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async ValueTask<Result> Handle(
        DeleteCategoryCommand request,
        CancellationToken cancellationToken
    )
    {
        Category? entity = await _dbContext.Categories.FirstOrDefaultAsync(
            x => x.Id == request.Id,
            cancellationToken: cancellationToken
        );
        if (entity is null)
        {
            return Error.NotFound("Category.NotFound", "Category not found");
        }
        _logger.LogInformation("Attempting to delete category {Name}", entity.Name);
        _dbContext.Categories.Remove(entity);
        int rows = await _unitOfWork.SaveChangesAsync(cancellationToken);
        if (rows < 1)
        {
            return Error.Failure("Database.Delete", "Failed to delete category");
        }
        _logger.LogInformation("Category {Name} deleted", entity.Name);
        return Result.Success();
    }
}
