namespace Application.Categories.Delete;

public sealed class DeleteCategoryCommand : ICommand
{
    public Guid Id { get; set; }

    public DeleteCategoryCommand(Guid id) => Id = id;
}

internal sealed class DeleteCategoryCommandValidator : AbstractValidator<DeleteCategoryCommand>
{
    public DeleteCategoryCommandValidator()
    {
        RuleFor(x => x.Id).NotEmpty().WithMessage("Id is required.");
    }
}
