<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="FluentValidation" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" />
    <PackageReference Include="Newtonsoft.Json" />
    <PackageReference Include="Orchestrix.Mediator" />
    <PackageReference Include="Orchestrix.Mediator.SourceGenerators" />
    <PackageReference Include="QuestPDF" />
    <PackageReference Include="Serilog" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Shared\Shared.csproj" />
    <ProjectReference Include="..\Domain\Domain.csproj" />
  </ItemGroup>

</Project>
