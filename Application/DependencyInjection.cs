using BuildingBlocks.Application.Behaviors;
using Application.Common.Options;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Orchestrix.Mediator;

namespace Application;

public static class DependencyInjection
{
    public static IServiceCollection AddApplication(
        this IServiceCollection services,
        IConfiguration configuration
    ) => services.ConfigureMediator().ConfigureOptions(configuration);

    private static IServiceCollection ConfigureMediator(this IServiceCollection services)
    {
        services.AddOrchestrix(config =>
        {
            config.UseSourceGenerator();
            config.RegisterHandlersFromAssemblies(typeof(DependencyInjection).Assembly);

            config.AddOpenBehavior(typeof(RequestLoggingPipelineBehavior<,>));
            config.AddOpenBehavior(typeof(ValidationBehavior<,>));
        });
        return services;
    }

    private static IServiceCollection ConfigureOptions(
        this IServiceCollection services,
        IConfiguration configuration
    ) =>
        services
            .Configure<MailSettingsOption>(configuration.GetSection("MailSettings"));
}
