using System.Reflection;
using FluentValidation.Results;
using Orchestrix.Mediator;

namespace BuildingBlocks.Application.Behaviors;

public class ValidationBehavior<TRequest, TResponse>(IEnumerable<IValidator<TRequest>> validators)
    : IPipelineBehavior<TRequest, TResponse>
    where TRequest : class, IRequest<TResponse>
{
    public async ValueTask<TResponse> Handle(
        TRequest request,
        RequestHandlerDelegate<TResponse> next,
        CancellationToken cancellationToken
    )
    {
        ArgumentNullException.ThrowIfNull(next);

        if (!validators.Any())
        {
            return await next(cancellationToken).ConfigureAwait(false);
        }

        ValidationContext<TRequest> context = new(request);

        ValidationResult[] validationResults = await Task.WhenAll(
                validators.Select(v => v.ValidateAsync(context, cancellationToken))
            )
            .ConfigureAwait(false);

        var failures = validationResults
            .Where(r => r.Errors.Count > 0)
            .SelectMany(r => r.Errors)
            .ToList();

        if (failures.Count <= 0)
        {
            return await next(cancellationToken).ConfigureAwait(false);
        }

        if (
            typeof(TResponse).IsGenericType
            && typeof(TResponse).GetGenericTypeDefinition() == typeof(Result)
        )
        {
            Type resultType = typeof(TResponse).GetGenericArguments()[0];
            MethodInfo? invalidMethod = typeof(Result<>)
                .MakeGenericType(resultType)
                .GetMethod(nameof(Result<TResponse>.Failure), [typeof(Error)]);
            if (invalidMethod is not null)
            {
                object? response = invalidMethod.Invoke(
                    null,
                    [Error.Validation("validation_error", failures.First().ErrorMessage)]
                );
                if (response is not null)
                {
                    return (TResponse)response;
                }
            }
        }

        if (typeof(TResponse) == typeof(Result))
        {
            return (TResponse)
                (object)
                    Result.Failure(
                        Error.Validation("validation_error", failures.First().ErrorMessage)
                    );
        }

        throw new ValidationException(failures);
    }
}
