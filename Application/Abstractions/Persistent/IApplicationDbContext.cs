using Domain.AuditLogs;
using Domain.Categories;
using Domain.Courses;
using Domain.Lessons;
using Domain.Payments;
using Domain.Progresses;
using Domain.Subscriptions;
using Domain.Users;
using Microsoft.EntityFrameworkCore;

namespace Application.Abstractions.Persistent;

public interface IApplicationDbContext
{
    DbSet<AuditLog> AuditLogs { get; set; }
    DbSet<Category> Categories { get; set; }
    DbSet<Course> Courses { get; set; }
    DbSet<Lesson> Lessons { get; set; }
    DbSet<Payment> Payments { get; set; }
    DbSet<Progress> Progresses { get; set; }
    DbSet<Subscription> Subscriptions { get; set; }
    DbSet<User> Users { get; set; }
}
