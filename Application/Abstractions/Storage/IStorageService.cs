using Microsoft.AspNetCore.Http;
using Shared;

namespace Application.Abstractions.Storage;

public interface IStorageService
{
    Task<Result<string>> GetPresignedUrl(
        string folder,
        string fileName,
        TimeSpan? expiresIn,
        CancellationToken cancellationToken = default
    );

    Task<Result<FileMetadata>> GetFileMetadataAsync(
        string folder,
        string fileName,
        CancellationToken cancellationToken = default
    );

    Task<Result<FileMetadata>> UploadAsync(
        string folder,
        IFormFile file,
        CancellationToken cancellationToken = default
    );

    Task<Result<FileMetadata>> UploadAsync(
        string folder,
        string fileName,
        Stream stream,
        CancellationToken cancellationToken = default
    );

    Task<Result<List<FileMetadata>>> UploadBulkAsync(
        string folder,
        IFormFileCollection files,
        CancellationToken cancellationToken = default
    );

    Task<Result<List<FileMetadata>>> UploadBulkAsync(
        string folder,
        ICollection<IFormFile> files,
        CancellationToken cancellationToken = default
    );

    Task<Result<List<FileMetadata>>> UploadBulkAsync(
        string folder,
        ICollection<(string fileName, Stream stream)> files,
        CancellationToken cancellationToken = default
    );

    Task<Result> DeleteFileAsync(
        string folder,
        string fileName,
        CancellationToken cancellationToken = default
    );

    Task<Result> DeleteFolderAsync(string folder, CancellationToken cancellationToken = default);
}

public record FileMetadata(
    string Name,
    string ContentType,
    long Size,
    string ETag,
    string PublicUrl,
    DateTime LastModifiedAt
);
