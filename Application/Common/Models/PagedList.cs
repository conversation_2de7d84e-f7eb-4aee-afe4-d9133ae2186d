using Microsoft.EntityFrameworkCore;

namespace Application.Common.Models;

public sealed class PagedList<T>
{
    public int Page { get; private init; }
    public int PageSize { get; private init; }
    public int TotalCount { get; private init; }
    public IReadOnlyCollection<T> Items { get; private set; } = [];
    public bool HasNextPage => Page * PageSize < TotalCount;

    public bool HasPreviousPage =>
        Page > 1 && PageSize > 0 && Page * PageSize - TotalCount <= PageSize;

    public static PagedList<T> Create(IQueryable<T> source, int page, int pageSize)
    {
        if (page < 1 || pageSize < 1)
        {
            throw new ArgumentException("Page and page size must be greater than 0");
        }

        int totalCount = source.Count();
        var items = source.Skip((page - 1) * pageSize).Take(pageSize).ToList();
        return new PagedList<T>
        {
            Page = page,
            PageSize = pageSize,
            TotalCount = totalCount,
            Items = items.AsReadOnly(),
        };
    }

    public static async Task<PagedList<T>> CreateAsync(IQueryable<T> source, int page, int pageSize, CancellationToken cancellationToken = default)
    {
        if (page < 1 || pageSize < 1)
        {
            throw new ArgumentException("Page and page size must be greater than 0");
        }

        int totalCount = await source.CountAsync(cancellationToken: cancellationToken);
        List<T> items = await source.Skip((page - 1) * pageSize).Take(pageSize).ToListAsync(cancellationToken: cancellationToken);
        return new PagedList<T>
        {
            Page = page,
            PageSize = pageSize,
            TotalCount = totalCount,
            Items = items.AsReadOnly(),
        };
    }
}
