namespace Application.Common.Models;

public class GetListRequest
{
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public List<GetListSort>? Sorts { get; set; }
    public List<GetListFilter>? Filters { get; set; }
}

public class GetListSort
{
    public string Field { get; set; } = null!;
    public string Order { get; set; } = "asc";
}

public class GetListFilter
{
    public string Field { get; set; } = null!;
    public string Value { get; set; } = null!;
}

public class GetListRequestValidator : AbstractValidator<GetListRequest>
{
    public GetListRequestValidator()
    {
        RuleFor(x => x.Page)
            .GreaterThanOrEqualTo(1)
            .WithMessage("Page must be greater than or equal to 1");
        RuleFor(x => x.PageSize)
            .GreaterThanOrEqualTo(1)
            .WithMessage("Page size must be greater than or equal to 1")
            .LessThanOrEqualTo(100)
            .WithMessage("Page size must be less than or equal to 100");
        RuleForEach(x => x.Sorts)
            .SetValidator(new GetListSortValidator()!)
            .When(x => x.Sorts != null);
        RuleForEach(x => x.Filters)
            .SetValidator(new GetListFilterValidator())
            .When(x => x.Filters != null);
    }
}

public class GetListSortValidator : AbstractValidator<GetListSort>
{
    public GetListSortValidator()
    {
        RuleFor(x => x.Field).NotEmpty().WithMessage("Sort field must not be empty");
        RuleFor(x => x.Order)
            .NotEmpty()
            .WithMessage("Sort order must not be empty")
            .Must(x => x is "asc" or "desc")
            .WithMessage("Sort order must be 'asc' or 'desc'");
    }
}

public class GetListFilterValidator : AbstractValidator<GetListFilter>
{
    public GetListFilterValidator()
    {
        RuleFor(x => x.Field).NotEmpty().WithMessage("Filter field must not be empty");
        RuleFor(x => x.Value).NotEmpty().WithMessage("Filter value must not be empty");
    }
}
